"""
Centralized AI Views
Universal AI endpoints that can be used across the entire application
No more duplicate view code!
"""

import json
import logging
import time
from datetime import datetime
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated

# Rate limiting decorator (fallback if django-ratelimit not available)
try:
    from django_ratelimit.decorators import ratelimit
    RATE_LIMITING_AVAILABLE = True
except ImportError:
    # Fallback decorator if django-ratelimit is not installed
    def ratelimit(key=None, rate=None, method=None, block=True):
        def decorator(func):
            return func
        return decorator
    RATE_LIMITING_AVAILABLE = False

from .ai_service import (
    ai_chat,
    ai_analyze_business,
    ai_analyze_text,
    ai_generate_intelligent_content,
    ai_get_status,
    ai_is_available,
    ai_chat_auto,
    ai_analyze_business_auto,
    ai_analyze_text_auto,
    detect_language
)

# Import new AI engines
try:
    from ai_models.predictive_engine import PredictiveAnalyticsEngine
    from ai_models.computer_vision import ComputerVisionEngine
    from ai_models.voice_ai import VoiceAIEngine
    ADVANCED_AI_AVAILABLE = True
except ImportError:
    ADVANCED_AI_AVAILABLE = False

logger = logging.getLogger(__name__)


# ========================================
# ENHANCED AI VIEWS - ADVANCED FEATURES
# ========================================

class PredictiveAnalyticsView(APIView):
    """
    Advanced predictive analytics for business success, market trends, and risk assessment
    """
    permission_classes = [IsAuthenticated]

    @ratelimit(key='user', rate='10/m', method='POST', block=True)
    def post(self, request):
        try:
            analysis_type = request.data.get('analysis_type', 'success_prediction')
            business_data = request.data.get('business_data', {})

            if not ADVANCED_AI_AVAILABLE:
                return Response({
                    'error': 'Advanced AI features not available',
                    'note': 'Install required ML libraries for predictive analytics'
                }, status=503)

            # Get predictive engine instance
            engine = PredictiveAnalyticsEngine.get_instance()

            # Route to appropriate analysis
            if analysis_type == 'success_prediction':
                result = engine.predict_business_success(business_data)
            elif analysis_type == 'market_forecast':
                industry = request.data.get('industry', 'technology')
                timeframe = request.data.get('timeframe_days', 90)
                result = engine.forecast_market_trends(industry, timeframe)
            elif analysis_type == 'risk_assessment':
                result = engine.assess_business_risks(business_data)
            elif analysis_type == 'investment_readiness':
                result = engine.generate_investment_readiness_score(business_data)
            # Enhanced predictive analytics methods
            elif analysis_type == 'failure_prediction':
                historical_data = request.data.get('historical_data', [])
                result = engine.predict_startup_failure_with_early_warning(business_data, historical_data)
            elif analysis_type == 'timing_optimization':
                market_context = request.data.get('market_context', {})
                result = engine.optimize_market_timing(business_data, market_context)
            elif analysis_type == 'competitor_analysis':
                competitor_data = request.data.get('competitor_data', [])
                result = engine.analyze_competitors_with_automation(business_data, competitor_data)
            elif analysis_type == 'cac_prediction':
                marketing_data = request.data.get('marketing_data', {})
                result = engine.predict_customer_acquisition_cost(business_data, marketing_data)
            else:
                return Response({
                    'error': 'Invalid analysis type',
                    'available_types': [
                        'success_prediction', 'market_forecast', 'risk_assessment', 'investment_readiness',
                        'failure_prediction', 'timing_optimization', 'competitor_analysis', 'cac_prediction'
                    ]
                }, status=400)

            return Response({
                'success': True,
                'analysis_type': analysis_type,
                'result': result,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"Predictive analytics error: {e}")
            return Response({
                'error': 'Predictive analytics failed',
                'details': str(e)
            }, status=500)


class ComputerVisionView(APIView):
    """
    Computer vision analysis for documents, images, and visual content
    """
    permission_classes = [IsAuthenticated]

    @ratelimit(key='user', rate='5/m', method='POST', block=True)
    def post(self, request):
        try:
            analysis_type = request.data.get('analysis_type', 'document_analysis')

            if not ADVANCED_AI_AVAILABLE:
                return Response({
                    'error': 'Computer vision features not available',
                    'note': 'Install required CV libraries for image analysis'
                }, status=503)

            # Get computer vision engine instance
            engine = ComputerVisionEngine.get_instance()

            # Handle file upload
            if 'image' not in request.FILES and 'images' not in request.FILES:
                return Response({
                    'error': 'No image file provided',
                    'note': 'Upload image file for analysis'
                }, status=400)

            # Route to appropriate analysis
            if analysis_type == 'document_analysis':
                image_file = request.FILES['image']
                image_data = image_file.read()
                document_type = request.data.get('document_type', 'business_plan')
                result = engine.analyze_document(image_data, document_type)

            elif analysis_type == 'pitch_deck_analysis':
                if 'images' in request.FILES:
                    images = request.FILES.getlist('images')
                    images_data = [img.read() for img in images]
                else:
                    images_data = [request.FILES['image'].read()]
                result = engine.analyze_pitch_deck(images_data)

            elif analysis_type == 'logo_analysis':
                image_file = request.FILES['image']
                image_data = image_file.read()
                result = engine.analyze_logo_brand(image_data)

            elif analysis_type == 'chart_analysis':
                image_file = request.FILES['image']
                image_data = image_file.read()
                result = engine.extract_chart_data(image_data)

            else:
                return Response({
                    'error': 'Invalid analysis type',
                    'available_types': ['document_analysis', 'pitch_deck_analysis', 'logo_analysis', 'chart_analysis']
                }, status=400)

            return Response({
                'success': True,
                'analysis_type': analysis_type,
                'result': result,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"Computer vision error: {e}")
            return Response({
                'error': 'Computer vision analysis failed',
                'details': str(e)
            }, status=500)


class VoiceAIView(APIView):
    """
    Voice AI for speech recognition, synthesis, and voice analysis
    """
    permission_classes = [IsAuthenticated]

    @ratelimit(key='user', rate='10/m', method='POST', block=True)
    def post(self, request):
        try:
            action = request.data.get('action', 'transcribe')

            if not ADVANCED_AI_AVAILABLE:
                return Response({
                    'error': 'Voice AI features not available',
                    'note': 'Install required voice AI libraries'
                }, status=503)

            # Get voice AI engine instance
            engine = VoiceAIEngine.get_instance()

            # Route to appropriate action
            if action == 'transcribe':
                if 'audio' not in request.FILES:
                    return Response({
                        'error': 'No audio file provided',
                        'note': 'Upload audio file for transcription'
                    }, status=400)

                audio_file = request.FILES['audio']
                audio_data = audio_file.read()
                language = request.data.get('language', 'auto')
                result = engine.transcribe_audio(audio_data, language)

            elif action == 'synthesize':
                text = request.data.get('text', '')
                if not text:
                    return Response({
                        'error': 'No text provided for synthesis'
                    }, status=400)

                language = request.data.get('language', 'en')
                voice_style = request.data.get('voice_style', 'neutral')
                result = engine.synthesize_speech(text, language, voice_style)

            elif action == 'analyze_sentiment':
                text = request.data.get('transcribed_text', '')
                if not text:
                    return Response({
                        'error': 'No transcribed text provided for sentiment analysis'
                    }, status=400)

                result = engine.analyze_voice_sentiment(text)

            elif action == 'process_command':
                text = request.data.get('transcribed_text', '')
                if not text:
                    return Response({
                        'error': 'No transcribed text provided for command processing'
                    }, status=400)

                result = engine.process_voice_command(text)

            elif action == 'transcribe_meeting':
                if 'audio' not in request.FILES:
                    return Response({
                        'error': 'No audio file provided for meeting transcription'
                    }, status=400)

                audio_file = request.FILES['audio']
                audio_data = audio_file.read()
                participants = request.data.get('participants', [])
                result = engine.transcribe_meeting(audio_data, participants)

            else:
                return Response({
                    'error': 'Invalid action',
                    'available_actions': ['transcribe', 'synthesize', 'analyze_sentiment', 'process_command', 'transcribe_meeting']
                }, status=400)

            return Response({
                'success': True,
                'action': action,
                'result': result,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"Voice AI error: {e}")
            return Response({
                'error': 'Voice AI processing failed',
                'details': str(e)
            }, status=500)


# Temporarily disabled rate limiting to fix chat functionality
# @method_decorator(ratelimit(key='user', rate='50/h', method='POST', block=True), name='post')
class UniversalChatView(APIView):
    """
    Universal chat endpoint used across the entire application
    Replaces all other chat views
    Enhanced with rate limiting
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Process chat message"""
        request_id = f"chat_{request.user.id}_{int(time.time() * 1000)}"
        try:
            logger.info(f"[{request_id}] Universal chat request START - User: {request.user.id} ({request.user.username})")
            logger.info(f"[{request_id}] Request headers: {dict(request.headers)}")

            data = request.data
            logger.info(f"[{request_id}] Request data: {data}")

            message = data.get('message', '').strip()
            language = data.get('language', 'auto')  # auto-detect by default
            logger.info(f"[{request_id}] Extracted message: '{message}', language: '{language}'")
            
            if not message:
                logger.warning(f"[{request_id}] Empty message provided")
                return Response(
                    {'error': 'Message is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Use auto-detection if language is 'auto'
            logger.info(f"[{request_id}] Calling AI service - language: {language}")
            if language == 'auto':
                logger.info(f"[{request_id}] Using ai_chat_auto")
                result = ai_chat_auto(message, request.user.id)
            else:
                logger.info(f"[{request_id}] Using ai_chat with language: {language}")
                result = ai_chat(message, language, request.user.id)

            logger.info(f"[{request_id}] AI service result: {result}")
            logger.info(f"[{request_id}] Universal chat request SUCCESS")
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"[{request_id}] Universal chat FAILED - Error: {e}")
            import traceback
            logger.error(f"[{request_id}] Universal chat traceback: {traceback.format_exc()}")

            # Classify error type for better user messaging
            error_type = "unknown"
            user_message = "An error occurred while processing your message"

            if "authentication" in str(e).lower() or "unauthorized" in str(e).lower():
                error_type = "auth"
                user_message = "Authentication error. Please try logging in again."
            elif "timeout" in str(e).lower() or "connection" in str(e).lower():
                error_type = "network"
                user_message = "Network timeout. Please check your connection and try again."
            elif "rate limit" in str(e).lower():
                error_type = "rate_limit"
                user_message = "Too many requests. Please wait a moment and try again."

            logger.error(f"[{request_id}] Error classified as: {error_type}")

            return Response(
                {
                    'success': False,
                    'error': str(e),
                    'error_type': error_type,
                    'message': user_message,
                    'traceback': traceback.format_exc(),  # Add traceback for debugging
                    'request_id': request_id  # Add request ID for tracking
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@method_decorator(ratelimit(key='user', rate='20/h', method='POST', block=True), name='post')
class UniversalBusinessAnalysisView(APIView):
    """
    Universal business analysis endpoint used across the entire application
    Replaces all other business analysis views
    Enhanced with rate limiting
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Analyze business idea"""
        try:
            data = request.data
            business_idea_id = data.get('business_idea_id')
            business_text = data.get('business_idea', '')
            language = data.get('language', 'auto')
            
            # Get business idea text from database if ID provided
            if business_idea_id and not business_text:
                try:
                    from incubator.models import BusinessIdea
                    business_idea = BusinessIdea.objects.get(
                        id=business_idea_id, 
                        user=request.user
                    )
                    business_text = f"{business_idea.title}: {business_idea.description}"
                except BusinessIdea.DoesNotExist:
                    return Response(
                        {'error': 'Business idea not found'},
                        status=status.HTTP_404_NOT_FOUND
                    )
                except ImportError:
                    # If incubator app not available, continue with provided text
                    pass
            
            if not business_text:
                return Response(
                    {'error': 'Business idea text is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Use auto-detection if language is 'auto'
            if language == 'auto':
                result = ai_analyze_business_auto(business_text, request.user.id)
            else:
                result = ai_analyze_business(business_text, language, request.user.id)
            
            # Add business idea ID to response
            result['business_idea_id'] = business_idea_id
            
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Universal business analysis error: {e}")
            return Response(
                {
                    'success': False,
                    'error': str(e),
                    'analysis': 'Business analysis is temporarily unavailable'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@method_decorator(ratelimit(key='user', rate='30/h', method='POST', block=True), name='post')
class UniversalTextAnalysisView(APIView):
    """
    Universal text analysis endpoint used across the entire application
    Replaces all other text analysis views
    Enhanced with rate limiting
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Analyze text content"""
        try:
            data = request.data
            text = data.get('text', '').strip()
            language = data.get('language', 'auto')
            
            if not text:
                return Response(
                    {'error': 'Text is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Use auto-detection if language is 'auto'
            if language == 'auto':
                result = ai_analyze_text_auto(text, request.user.id)
            else:
                result = ai_analyze_text(text, language, request.user.id)
            
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Universal text analysis error: {e}")
            return Response(
                {
                    'success': False,
                    'error': str(e),
                    'analysis': 'Text analysis is temporarily unavailable'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UniversalAIStatusView(APIView):
    """
    Universal AI status endpoint used across the entire application
    Replaces all other status views
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get AI service status"""
        try:
            status_info = ai_get_status()
            return Response(status_info, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Universal status error: {e}")
            return Response(
                {
                    'centralized_service': False,
                    'available': False,
                    'error': str(e)
                },
                status=status.HTTP_200_OK
            )


@method_decorator(ratelimit(key='user', rate='10/h', method='POST', block=True), name='post')
class IntelligentContentView(APIView):
    """
    New intelligent content generation endpoint
    Advanced AI feature for business intelligence
    Enhanced with rate limiting
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Generate intelligent content based on type and context"""
        try:
            data = request.data
            content_type = data.get('content_type', '').strip()
            context = data.get('context', {})
            language = data.get('language', 'auto')

            if not content_type:
                return Response(
                    {'error': 'Content type is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not context:
                return Response(
                    {'error': 'Context is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Auto-detect language if needed
            if language == 'auto':
                # Try to detect from context
                text_for_detection = context.get('business_idea', '') or context.get('text', '')
                if text_for_detection:
                    language = detect_language(text_for_detection)
                else:
                    language = 'en'

            result = ai_generate_intelligent_content(
                content_type,
                context,
                language,
                request.user.id
            )

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Intelligent content generation error: {e}")
            return Response(
                {
                    'success': False,
                    'error': str(e),
                    'content': 'Intelligent content generation is temporarily unavailable'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class LanguageDetectionView(APIView):
    """
    Enhanced language detection endpoint
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Detect language of provided text"""
        try:
            data = request.data
            text = data.get('text', '').strip()

            if not text:
                return Response(
                    {'error': 'Text is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            detected_language = detect_language(text)

            # Calculate confidence based on text length and characteristics
            confidence = 'high' if len(text) > 100 else 'medium' if len(text) > 20 else 'low'

            return Response({
                'text': text[:100] + '...' if len(text) > 100 else text,
                'detected_language': detected_language,
                'confidence': confidence,
                'text_length': len(text),
                'success': True
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Language detection error: {e}")
            return Response(
                {
                    'success': False,
                    'error': str(e),
                    'detected_language': 'en',
                    'confidence': 'low'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Function-based views for simple usage
@csrf_exempt
@login_required
def universal_chat(request):
    """Universal function-based chat endpoint"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            message = data.get('message', '').strip()
            language = data.get('language', 'auto')
            
            if not message:
                return JsonResponse({'error': 'Message is required'}, status=400)

            if language == 'auto':
                result = ai_chat_auto(message, request.user.id)
            else:
                result = ai_chat(message, language, request.user.id)
            
            return JsonResponse(result)

        except Exception as e:
            logger.error(f"Universal function chat error: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e),
                'message': 'An error occurred while processing your message'
            }, status=500)
    
    return JsonResponse({'error': 'Method not allowed'}, status=405)


@csrf_exempt
@login_required
def universal_business_analysis(request):
    """Universal function-based business analysis endpoint"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            business_text = data.get('business_idea', '').strip()
            language = data.get('language', 'auto')
            
            if not business_text:
                return JsonResponse({'error': 'Business idea is required'}, status=400)

            if language == 'auto':
                result = ai_analyze_business_auto(business_text, request.user.id)
            else:
                result = ai_analyze_business(business_text, language, request.user.id)
            
            return JsonResponse(result)

        except Exception as e:
            logger.error(f"Universal function business analysis error: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e),
                'analysis': 'Business analysis is temporarily unavailable'
            }, status=500)
    
    return JsonResponse({'error': 'Method not allowed'}, status=405)


@csrf_exempt
def universal_ai_status(request):
    """Universal function-based status endpoint"""
    if request.method == 'GET':
        try:
            status_info = ai_get_status()
            return JsonResponse(status_info)

        except Exception as e:
            logger.error(f"Universal function status error: {e}")
            return JsonResponse({
                'centralized_service': False,
                'available': False,
                'error': str(e)
            })
    
    return JsonResponse({'error': 'Method not allowed'}, status=405)


@csrf_exempt
def universal_ai_test(request):
    """Universal test endpoint to verify everything works"""
    if request.method == 'GET':
        try:
            # Test the service
            test_result = ai_chat("Hello, are you working?", "en")
            status_result = ai_get_status()
            
            return JsonResponse({
                'test_passed': True,
                'test_response': test_result,
                'service_status': status_result,
                'message': 'Universal AI service is working correctly!',
                'centralized': True
            })

        except Exception as e:
            return JsonResponse({
                'test_passed': False,
                'error': str(e),
                'message': 'Universal AI service test failed'
            }, status=500)
    
    return JsonResponse({'error': 'Method not allowed'}, status=405)


@csrf_exempt
def detect_text_language(request):
    """Utility endpoint to detect text language"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            text = data.get('text', '').strip()
            
            if not text:
                return JsonResponse({'error': 'Text is required'}, status=400)
            
            detected_language = detect_language(text)
            
            return JsonResponse({
                'text': text[:100] + '...' if len(text) > 100 else text,
                'detected_language': detected_language,
                'confidence': 'high' if len(text) > 50 else 'medium'
            })

        except Exception as e:
            return JsonResponse({
                'error': str(e),
                'detected_language': 'en'
            }, status=500)
    
    return JsonResponse({'error': 'Method not allowed'}, status=405)
