#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

# Now test with the actual user ID from the web request
try:
    print("=== Actual User Debug Test ===")
    
    from django.contrib.auth.models import User
    from core.ai_service import ai_chat_auto
    
    # Get the actual user that's making the web request (ID 72)
    try:
        user = User.objects.get(id=72)
        print(f"Found user: {user.username} (ID: {user.id})")
    except User.DoesNotExist:
        print("User ID 72 not found, trying to find test_user_e2e_2025...")
        try:
            user = User.objects.get(username='test_user_e2e_2025')
            print(f"Found user: {user.username} (ID: {user.id})")
        except User.DoesNotExist:
            print("test_user_e2e_2025 not found either")
            users = User.objects.all()
            print(f"Available users: {[(u.id, u.username) for u in users]}")
            exit(1)
    
    print(f"\nTesting with user: {user.username} (ID: {user.id})")
    
    # Test the exact message from the web request
    test_message = "Hello! Testing after fixing the rate limiting issue."
    
    print(f"Message: '{test_message}'")
    
    try:
        result = ai_chat_auto(test_message, user.id)
        print(f"SUCCESS: {result}")
    except Exception as e:
        print(f"ERROR in ai_chat_auto with user {user.id}: {e}")
        import traceback
        traceback.print_exc()
        
        # Let's also test with the admin user to compare
        print(f"\nComparing with admin user (ID 43)...")
        try:
            admin_user = User.objects.get(id=43)
            admin_result = ai_chat_auto(test_message, admin_user.id)
            print(f"Admin user SUCCESS: {admin_result}")
        except Exception as e2:
            print(f"Admin user also failed: {e2}")

except Exception as e:
    print(f"MAIN ERROR: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 50)
print("Actual user debug test completed")