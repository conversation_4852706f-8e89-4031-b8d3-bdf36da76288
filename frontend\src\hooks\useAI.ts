import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import {
  fetchAIStatus,
  analyzeBusinessIdea,
  sendChatMessage,
  clearAnalysisError,
  clearChatError,
  clearStatusError,
  clearChatMessages,
  setCurrentAnalysis,
  updateLastUpdated
} from '../store/aiSlice';

/**
 * Redux-based AI hook
 * Provides centralized AI state management using Redux
 */
export const useAI = () => {
  const dispatch = useAppDispatch();
  const aiState = useAppSelector((state) => state.ai);

  // Status operations
  const refreshStatus = useCallback(() => {
    dispatch(fetchAIStatus());
  }, [dispatch]);

  const clearStatusErrors = useCallback(() => {
    dispatch(clearStatusError());
  }, [dispatch]);

  // Business analysis operations
  const analyzeIdea = useCallback(async (params: {
    business_idea: string;
    business_idea_id?: number;
    language?: string;
  }) => {
    const result = await dispatch(analyzeBusinessIdea(params));
    return result;
  }, [dispatch]);

  const clearAnalysisErrors = useCallback(() => {
    dispatch(clearAnalysisError());
  }, [dispatch]);

  const setAnalysis = useCallback((analysis: any) => {
    dispatch(setCurrentAnalysis(analysis));
  }, [dispatch]);

  // Chat operations
  const sendMessage = useCallback(async (params: {
    message: string;
    language?: string;
    context?: any;
  }) => {
    const result = await dispatch(sendChatMessage(params));
    return result;
  }, [dispatch]);

  const clearChatErrors = useCallback(() => {
    dispatch(clearChatError());
  }, [dispatch]);

  const clearChat = useCallback(() => {
    dispatch(clearChatMessages());
  }, [dispatch]);

  // Utility operations
  const updateTimestamp = useCallback(() => {
    dispatch(updateLastUpdated());
  }, [dispatch]);

  return {
    // State
    status: aiState.status,
    isStatusLoading: aiState.isStatusLoading,
    statusError: aiState.statusError,
    
    currentAnalysis: aiState.currentAnalysis,
    isAnalyzing: aiState.isAnalyzing,
    analysisError: aiState.analysisError,
    analysisHistory: aiState.analysisHistory,
    
    chatMessages: aiState.chatMessages,
    isChatLoading: aiState.isChatLoading,
    chatError: aiState.chatError,
    
    isAvailable: aiState.isAvailable,
    lastUpdated: aiState.lastUpdated,

    // Actions
    refreshStatus,
    clearStatusErrors,
    
    analyzeIdea,
    clearAnalysisErrors,
    setAnalysis,
    
    sendMessage,
    clearChatErrors,
    clearChat,
    
    updateTimestamp,
  };
};

/**
 * Specialized hook for business analysis
 */
export const useBusinessAnalysis = () => {
  const {
    currentAnalysis,
    isAnalyzing,
    analysisError,
    analysisHistory,
    analyzeIdea,
    clearAnalysisErrors,
    setAnalysis,
    isAvailable
  } = useAI();

  const analyzeBusinessIdea = useCallback(async (
    businessIdea: string,
    businessIdeaId?: number,
    language?: string
  ) => {
    return await analyzeIdea({
      business_idea: businessIdea,
      business_idea_id: businessIdeaId,
      language: language || 'auto'
    });
  }, [analyzeIdea]);

  return {
    currentAnalysis,
    isAnalyzing,
    analysisError,
    analysisHistory,
    analyzeBusinessIdea,
    clearAnalysisErrors,
    setAnalysis,
    isAvailable
  };
};

/**
 * Specialized hook for AI chat
 */
export const useAIChat = () => {
  const {
    chatMessages,
    isChatLoading,
    chatError,
    sendMessage,
    clearChatErrors,
    clearChat,
    isAvailable
  } = useAI();

  const sendChatMessage = useCallback(async (
    message: string,
    language?: string,
    context?: any
  ) => {
    return await sendMessage({
      message,
      language: language || 'auto',
      context
    });
  }, [sendMessage]);

  return {
    chatMessages,
    isChatLoading,
    chatError,
    sendChatMessage,
    clearChatErrors,
    clearChat,
    isAvailable
  };
};

/**
 * Specialized hook for AI status
 */
export const useAIStatus = () => {
  const {
    status,
    isStatusLoading,
    statusError,
    refreshStatus,
    clearStatusErrors,
    isAvailable,
    lastUpdated
  } = useAI();

  return {
    status,
    isStatusLoading,
    statusError,
    refreshStatus,
    clearStatusErrors,
    isAvailable,
    lastUpdated
  };
};

export default useAI;
