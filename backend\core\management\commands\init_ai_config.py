from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from core.models import AIConfiguration
from core.ai_config import get_gemini_config, reload_gemini_config
import os


class Command(BaseCommand):
    help = 'Initialize AI configuration in database and test connection'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force update existing configuration',
        )

    def handle(self, *args, **options):
        self.stdout.write('🚀 Initializing AI Configuration...')
        
        # Get or create super admin user for configuration
        try:
            admin_user = User.objects.filter(is_superuser=True).first()
            if not admin_user:
                admin_user = User.objects.create_superuser(
                    username='admin',
                    email='<EMAIL>',
                    password='admin123'
                )
                self.stdout.write(self.style.SUCCESS('✅ Created admin user'))
        except Exception as e:
            self.stdout.write(self.style.WARNING(f'⚠️ Admin user setup: {e}'))
            admin_user = None

        # Initialize AI configuration from environment
        api_key = os.getenv('GEMINI_API_KEY', 'AIzaSyBLcSmyOVNJyKq_X6T3MOjio6XmZLliX5s')
        
        configs = [
            {
                'key': 'api_key',
                'value': api_key,
                'config_type': 'api_key',
                'is_sensitive': True,
                'description': 'Google Gemini API Key for AI services'
            },
            {
                'key': 'default_model',
                'value': 'gemini-1.5-flash',
                'config_type': 'model_config',
                'is_sensitive': False,
                'description': 'Default Gemini model to use'
            },            {
                'key': 'max_tokens',
                'value': '4000',
                'config_type': 'model_config',
                'is_sensitive': False,
                'description': 'Maximum tokens per request'
            },
            {
                'key': 'temperature',
                'value': '0.7',
                'config_type': 'model_config',
                'is_sensitive': False,
                'description': 'Model temperature for response creativity'
            }
        ]

        # Create or update configurations
        for config_data in configs:
            try:
                config, created = AIConfiguration.objects.update_or_create(
                    provider='gemini',
                    key=config_data['key'],
                    defaults={
                        'value': config_data['value'],
                        'config_type': config_data['config_type'],
                        'is_sensitive': config_data['is_sensitive'],
                        'description': config_data['description'],
                        'is_active': True,
                        'updated_by': admin_user,
                        'created_by': admin_user if created else None
                    }
                )
                
                action = 'Created' if created else 'Updated'
                self.stdout.write(
                    self.style.SUCCESS(f'✅ {action} config: {config_data["key"]}')
                )
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'❌ Failed to create config {config_data["key"]}: {e}')
                )

        # Reload configuration
        self.stdout.write('🔄 Reloading AI configuration...')
        try:
            reload_gemini_config()
            self.stdout.write(self.style.SUCCESS('✅ Configuration reloaded'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Failed to reload config: {e}'))

        # Test AI service
        self.stdout.write('🧪 Testing AI service...')
        try:
            config = get_gemini_config()
            status = config.get_status()
            
            self.stdout.write(f'Service: {status["service"]}')
            self.stdout.write(f'Available: {status["available"]}')
            self.stdout.write(f'Model: {status["model"]}')
            self.stdout.write(f'API Key Configured: {status["api_key_configured"]}')
            
            if status['available']:
                # Test with a simple request
                test_response = config.generate_content("Hello! Please respond with 'AI is working correctly'")
                if test_response:
                    self.stdout.write(self.style.SUCCESS(f'✅ AI Test Response: {test_response}'))
                else:
                    self.stdout.write(self.style.ERROR('❌ AI test failed - no response'))
            else:
                self.stdout.write(self.style.ERROR('❌ AI service is not available'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ AI test failed: {e}'))

        self.stdout.write(self.style.SUCCESS('🎉 AI Configuration initialization complete!'))