#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

# Now test the exact web request scenario
try:
    print("=== Web Chat Debug Test ===")
    
    from core.ai_service import ai_chat_auto
    from django.contrib.auth.models import User
    
    # Get the actual user that would be making the request
    print("Step 1: Getting user from database...")
    try:
        # Try to get the user that's logged in (assuming user ID 1 or first user)
        user = User.objects.first()
        if user:
            print(f"Found user: {user.username} (ID: {user.id})")
        else:
            print("No users found in database")
            exit(1)
    except Exception as e:
        print(f"Error getting user: {e}")
        exit(1)
    
    print("\nStep 2: Testing ai_chat_auto with actual user ID...")
    test_message = "Test message - are you working now?"
    
    try:
        result = ai_chat_auto(test_message, user.id)
        print(f"SUCCESS: {result}")
    except Exception as e:
        print(f"ERROR in ai_chat_auto: {e}")
        import traceback
        traceback.print_exc()
        
        # Let's also test the individual components
        print("\nStep 3: Testing individual components...")
        
        try:
            from core.ai_service import detect_language
            detected_lang = detect_language(test_message)
            print(f"Language detection: {detected_lang}")
        except Exception as e2:
            print(f"Error in detect_language: {e2}")
            
        try:
            from core.ai_service import ai_chat
            chat_result = ai_chat(test_message, 'en', user.id)
            print(f"Direct ai_chat: {chat_result}")
        except Exception as e3:
            print(f"Error in ai_chat: {e3}")
            import traceback
            traceback.print_exc()

except Exception as e:
    print(f"MAIN ERROR: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 50)
print("Web debug test completed")