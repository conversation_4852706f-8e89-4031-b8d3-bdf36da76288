from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from django.contrib.auth.models import User
from django.utils import timezone
from api.super_admin_permissions import IsSuperAdminUser
from .models import AIConfiguration
from .ai_config import get_gemini_config, reload_gemini_config
import logging

logger = logging.getLogger(__name__)


class AIConfigurationViewSet(viewsets.ViewSet):
    """
    ViewSet for managing AI configuration (Super Admin only)
    """
    permission_classes = [IsSuperAdminUser]
    
    def list(self, request):
        """Get all AI configurations"""
        try:
            configs = AIConfiguration.objects.filter(is_active=True)
            config_data = []
            
            for config in configs:
                config_data.append({
                    'id': config.id,
                    'provider': config.provider,
                    'key': config.key,
                    'value': config.get_display_value(),
                    'config_type': config.config_type,
                    'description': config.description,
                    'is_sensitive': config.is_sensitive,
                    'updated_at': config.updated_at.isoformat()
                })
            
            return Response({'configurations': config_data})
            
        except Exception as e:
            logger.error(f"Error fetching AI configurations: {str(e)}")
            return Response(
                {'error': 'Failed to fetch configurations'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def create(self, request):
        """Create new AI configuration"""
        try:
            data = request.data
            
            config = AIConfiguration.objects.create(
                provider=data.get('provider', 'gemini'),
                key=data.get('key'),
                value=data.get('value'),
                config_type=data.get('config_type', 'api_key'),
                is_sensitive=data.get('is_sensitive', False),
                description=data.get('description', ''),
                created_by=request.user,
                updated_by=request.user
            )
            
            # Reload configuration
            reload_gemini_config()
            
            return Response({
                'message': 'Configuration created successfully',
                'config_id': config.id
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"Error creating AI configuration: {str(e)}")
            return Response(
                {'error': 'Failed to create configuration'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def update(self, request, pk=None):
        """Update AI configuration"""
        try:
            config = AIConfiguration.objects.get(id=pk)
            data = request.data
            
            if 'value' in data:
                config.value = data['value']
            if 'description' in data:
                config.description = data['description']
            if 'is_active' in data:
                config.is_active = data['is_active']
            
            config.updated_by = request.user
            config.save()
            
            # Reload configuration
            reload_gemini_config()
            
            return Response({'message': 'Configuration updated successfully'})
            
        except AIConfiguration.DoesNotExist:
            return Response(
                {'error': 'Configuration not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error updating AI configuration: {str(e)}")
            return Response(
                {'error': 'Failed to update configuration'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def destroy(self, request, pk=None):
        """Delete AI configuration"""
        try:
            config = AIConfiguration.objects.get(id=pk)
            config.delete()
            
            # Reload configuration
            reload_gemini_config()
            
            return Response({'message': 'Configuration deleted successfully'})
            
        except AIConfiguration.DoesNotExist:
            return Response(
                {'error': 'Configuration not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error deleting AI configuration: {str(e)}")
            return Response(
                {'error': 'Failed to delete configuration'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AIStatusView(APIView):
    """
    Public AI status endpoint
    """
    permission_classes = [AllowAny]  # Allow unauthenticated access

    def get(self, request):
        """Get AI service status"""
        try:
            config = get_gemini_config()
            status_info = config.get_status()
            
            return Response({
                'success': True,
                'status': status_info,
                'timestamp': timezone.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error getting AI status: {str(e)}")
            return Response({
                'success': False,
                'error': 'Failed to get AI status',
                'available': False
            })
