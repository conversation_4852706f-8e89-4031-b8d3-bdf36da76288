/**
 * Enhanced Centralized AI API Service
 * Single source of truth for all AI operations across the frontend
 * Now with intelligent features, caching awareness, and proper error handling
 * Version 2.0 - Replaces ALL other AI services
 */

import { apiRequest } from './api';

// Enhanced AI Types
export interface CentralizedAIResponse {
  success: boolean;
  message?: string;
  analysis?: string;
  content?: string;
  error?: string;
  service: string;
  timestamp: string;
  user_id?: number;
  fallback_used?: boolean;
  cache_hit?: boolean;
  language?: string;
  content_type?: string;
}

export interface AIStatus {
  service: string;
  available: boolean;
  centralized_service: boolean;
  model: string;
  api_key_configured: boolean;
  version: string;
  features: {
    chat: boolean;
    business_analysis: boolean;
    text_analysis: boolean;
    multilingual: boolean;
    fallbacks: boolean;
    centralized_config: boolean;
    caching: boolean;
    rate_limiting: boolean;
    error_handling: boolean;
    context_awareness: boolean;
    intelligent_responses: boolean;
  };
  performance: {
    caching_enabled: boolean;
    rate_limiting_enabled: boolean;
    fallback_system: boolean;
    error_recovery: boolean;
  };
  timestamp: string;
}

export interface LanguageDetectionResponse {
  text: string;
  detected_language: string;
  confidence: string;
}

export interface BusinessAnalysisResponse {
  id?: string;
  business_idea: string;
  business_idea_id?: number;
  analysis: {
    business_information: any;
    market_analysis: any;
    feasibility_assessment: any;
    business_insights: any;
    strategic_recommendations: any;
    ml_insights: any;
  };
  summary: {
    recommendationCount: number;
    workflowType: string;
    hasMLInsights: boolean;
    hasCulturalAdaptations: boolean;
  };
  service: string;
  timestamp: string;
  language?: string;
}

/**
 * Enhanced Centralized AI API Service
 * This replaces ALL other AI API services in the application
 * Version 2.0 with intelligent features
 */
export const centralizedAiApi = {
  /**
   * Universal chat function - used across the entire application
   */
  chat: (
    message: string,
    language?: string,
    context?: any
  ): Promise<CentralizedAIResponse> =>
    apiRequest<CentralizedAIResponse>(
      '/ai/chat/',
      'POST',
      {
        message,
        language: language || 'auto',
        context
      }
    ),

  /**
   * Universal business analysis - used across the entire application
   */
  analyzeBusinessIdea: (data: {
    business_idea?: string;
    business_idea_id?: number;
    language?: string;
  }): Promise<CentralizedAIResponse> =>
    apiRequest<CentralizedAIResponse>(
      '/ai/business-analysis/',
      'POST',
      {
        business_idea: data.business_idea,
        business_idea_id: data.business_idea_id,
        language: data.language || 'auto'
      }
    ),

  /**
   * Universal text analysis - used across the entire application
   */
  analyzeText: (data: {
    text: string;
    language?: string;
  }): Promise<CentralizedAIResponse> =>
    apiRequest<CentralizedAIResponse>(
      '/ai/text-analysis/',
      'POST',
      {
        text: data.text,
        language: data.language || 'auto'
      }
    ),

  /**
   * NEW: Intelligent content generation - Advanced AI feature
   */
  generateIntelligentContent: (data: {
    content_type: string;
    context: Record<string, any>;
    language?: string;
  }): Promise<CentralizedAIResponse> =>
    apiRequest<CentralizedAIResponse>(
      '/ai/intelligent-content/',
      'POST',
      {
        content_type: data.content_type,
        context: data.context,
        language: data.language || 'auto'
      }
    ),

  /**
   * Get AI service status
   */
  getStatus: (): Promise<AIStatus> =>
    apiRequest<AIStatus>('/ai/status/'),

  /**
   * Test AI service
   */
  test: (): Promise<any> =>
    apiRequest<any>('/ai/test/'),

  /**
   * Enhanced language detection
   */
  detectLanguage: (text: string): Promise<LanguageDetectionResponse> =>
    apiRequest<LanguageDetectionResponse>(
      '/ai/detect-language/',
      'POST',
      { text }
    ),

  // Convenience methods with auto language detection
  /**
   * Chat with automatic language detection
   */
  chatAuto: (message: string): Promise<CentralizedAIResponse> =>
    centralizedAiApi.chat({ message, language: 'auto' }),

  /**
   * Analyze business with automatic language detection
   */
  analyzeBusinessAuto: (business_idea: string): Promise<CentralizedAIResponse> =>
    centralizedAiApi.analyzeBusinessIdea({ business_idea, language: 'auto' }),

  /**
   * Analyze text with automatic language detection
   */
  analyzeTextAuto: (text: string): Promise<CentralizedAIResponse> =>
    centralizedAiApi.analyzeText({ text, language: 'auto' }),

  // Language-specific methods
  /**
   * Chat in English
   */
  chatEnglish: (message: string): Promise<CentralizedAIResponse> =>
    centralizedAiApi.chat({ message, language: 'en' }),

  /**
   * Chat in Arabic
   */
  chatArabic: (message: string): Promise<CentralizedAIResponse> =>
    centralizedAiApi.chat({ message, language: 'ar' }),

  /**
   * Analyze business in English
   */
  analyzeBusinessEnglish: (business_idea: string): Promise<CentralizedAIResponse> =>
    centralizedAiApi.analyzeBusinessIdea({ business_idea, language: 'en' }),

  /**
   * Analyze business in Arabic
   */
  analyzeBusinessArabic: (business_idea: string): Promise<CentralizedAIResponse> =>
    centralizedAiApi.analyzeBusinessIdea({ business_idea, language: 'ar' }),

  // NEW: Intelligent content generation convenience methods
  /**
   * Generate business plan section
   */
  generateBusinessPlanSection: (data: {
    business_idea: string;
    section_type: string;
    industry?: string;
    language?: string;
  }): Promise<CentralizedAIResponse> =>
    centralizedAiApi.generateIntelligentContent({
      content_type: 'business_plan_section',
      context: {
        business_idea: data.business_idea,
        section_type: data.section_type,
        industry: data.industry || 'general'
      },
      language: data.language
    }),

  /**
   * Generate market analysis
   */
  generateMarketAnalysis: (data: {
    business_idea: string;
    target_market: string;
    language?: string;
  }): Promise<CentralizedAIResponse> =>
    centralizedAiApi.generateIntelligentContent({
      content_type: 'market_analysis',
      context: {
        business_idea: data.business_idea,
        target_market: data.target_market
      },
      language: data.language
    }),

  /**
   * Generate financial projections
   */
  generateFinancialProjections: (data: {
    business_idea: string;
    revenue_model: string;
    language?: string;
  }): Promise<CentralizedAIResponse> =>
    centralizedAiApi.generateIntelligentContent({
      content_type: 'financial_projection',
      context: {
        business_idea: data.business_idea,
        revenue_model: data.revenue_model
      },
      language: data.language
    }),

  /**
   * Generate risk assessment
   */
  generateRiskAssessment: (data: {
    business_idea: string;
    industry: string;
    language?: string;
  }): Promise<CentralizedAIResponse> =>
    centralizedAiApi.generateIntelligentContent({
      content_type: 'risk_assessment',
      context: {
        business_idea: data.business_idea,
        industry: data.industry
      },
      language: data.language
    }),
};



export default centralizedAiApi;
