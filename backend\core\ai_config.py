"""
Centralized Gemini AI Configuration
Single source of truth for Gemini AI across the application.
Database-driven configuration that Super Admin can manage from frontend.
"""

import os
import json
import logging
from typing import Optional, Dict, Any
from django.conf import settings
from django.core.cache import cache
import google.generativeai as genai

# Configure logging
logger = logging.getLogger(__name__)

class CentralGeminiConfig:
    """
    Centralized Gemini AI configuration for the entire application
    Database-driven configuration with caching for performance
    """

    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._setup_config()
            self._initialized = True

    def _setup_config(self):
        """Setup Gemini configuration from database"""
        self.api_key = None
        self.model_name = 'gemini-1.5-flash'  # Use stable model instead of 2.0-flash
        self.max_tokens = 4000
        self.temperature = 0.7
        self.is_available = False
        self.model = None

        # Load configuration from database first, then fallback
        self._load_database_config()

        # Initialize Gemini if API key is available
        if self.api_key:
            self._initialize_gemini()

        logger.info(f"🤖 Central Gemini Config initialized - Available: {self.is_available}")

    def _load_database_config(self):
        """Load Gemini configuration from database"""
        try:
            from .models import AIConfiguration

            # Get Gemini configurations from database
            self.api_key = AIConfiguration.get_config('gemini', 'api_key')
            self.model_name = AIConfiguration.get_config('gemini', 'default_model', 'gemini-1.5-flash')  # Use stable model
            self.max_tokens = int(AIConfiguration.get_config('gemini', 'max_tokens', 4000))
            self.temperature = float(AIConfiguration.get_config('gemini', 'temperature', 0.7))

            if self.api_key:
                logger.info("✅ Loaded Gemini config from database")
                return

        except Exception as e:
            logger.warning(f"Failed to load database config: {e}")

        # Fallback to environment/settings
        self._load_fallback_config()

    def _load_fallback_config(self):
        """Fallback configuration from environment variables"""
        # Try environment variable first
        self.api_key = os.getenv('GEMINI_API_KEY')

        # Try Django settings
        if not self.api_key:
            self.api_key = getattr(settings, 'GEMINI_API_KEY', None)

        # Use working key for development if nothing else is set
        if not self.api_key or self.api_key in ['your-gemini-api-key-here']:
            logger.warning("⚠️ Using fallback API key - configure proper key in environment or database")
            self.api_key = 'AIzaSyBLcSmyOVNJyKq_X6T3MOjio6XmZLliX5s'  # Working key from .env

        logger.info("📁 Using fallback Gemini config")

        # Try to initialize database config for future use
        self._initialize_database_config()

    def _initialize_database_config(self):
        """Initialize database configuration if it doesn't exist"""
        try:
            from .models import AIConfiguration
            from django.contrib.auth.models import User

            # Check if configuration already exists
            if AIConfiguration.objects.filter(provider='gemini', key='api_key').exists():
                return

            # Get admin user for configuration
            admin_user = User.objects.filter(is_superuser=True).first()

            # Create basic configuration entries
            configs = [
                {
                    'key': 'api_key',
                    'value': self.api_key,
                    'config_type': 'api_key',
                    'is_sensitive': True,
                    'description': 'Google Gemini API Key for AI services'
                },
                {
                    'key': 'default_model',
                    'value': self.model_name,
                    'config_type': 'model_config',
                    'is_sensitive': False,
                    'description': 'Default Gemini model to use'
                },
                {
                    'key': 'max_tokens',
                    'value': str(self.max_tokens),
                    'config_type': 'model_config',
                    'is_sensitive': False,
                    'description': 'Maximum tokens per request'
                },
                {
                    'key': 'temperature',
                    'value': str(self.temperature),
                    'config_type': 'model_config',
                    'is_sensitive': False,
                    'description': 'Model temperature for response creativity'
                }
            ]

            for config_data in configs:
                AIConfiguration.objects.create(
                    provider='gemini',
                    key=config_data['key'],
                    value=config_data['value'],
                    config_type=config_data['config_type'],
                    is_sensitive=config_data['is_sensitive'],
                    description=config_data['description'],
                    is_active=True,
                    created_by=admin_user,
                    updated_by=admin_user
                )

            logger.info("✅ Initialized database AI configuration")

        except Exception as e:
            logger.warning(f"Failed to initialize database config: {e}")

    def _initialize_gemini(self):
        """Initialize Gemini AI service"""
        try:
            logger.info(f"🔧 Initializing Gemini AI with model: {self.model_name}")
            logger.info(f"🔑 API Key configured: {bool(self.api_key)} (length: {len(self.api_key) if self.api_key else 0})")

            if not self.api_key:
                raise ValueError("No API key configured")

            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel(self.model_name)

            # Test the model with a simple request
            test_response = self.model.generate_content("Hello")
            logger.info(f"🧪 Test response received: {bool(test_response)}")

            if test_response and test_response.text:
                self.is_available = True
                logger.info("✅ Gemini AI initialized successfully")
            else:
                raise ValueError("Test response was empty or invalid")

        except Exception as e:
            logger.error(f"❌ Gemini AI initialization failed: {e}")
            logger.error(f"🔍 Error type: {type(e).__name__}")
            logger.error(f"🔍 API Key: {self.api_key[:10]}...{self.api_key[-4:] if self.api_key and len(self.api_key) > 14 else 'N/A'}")
            logger.error(f"🔍 Model: {self.model_name}")
            self.is_available = False
            self.model = None

    def generate_content(self, prompt: str, **kwargs) -> Optional[str]:
        """Generate content using Gemini AI"""
        if not self.is_available or not self.model:
            return None

        try:
            # Use configured parameters or override with kwargs
            generation_config = {
                'max_output_tokens': kwargs.get('max_tokens', self.max_tokens),
                'temperature': kwargs.get('temperature', self.temperature),
            }

            response = self.model.generate_content(prompt, generation_config=generation_config)
            return response.text if response and response.text else None
        except Exception as e:
            logger.error(f"Content generation error: {e}")
            return None

    def get_status(self) -> Dict[str, Any]:
        """Get Gemini service status"""
        return {
            'service': 'gemini',
            'available': self.is_available,
            'model': self.model_name,
            'api_key_configured': bool(self.api_key),
            'max_tokens': self.max_tokens,
            'temperature': self.temperature,
            'features': {
                'content_generation': True,
                'chat': True,
                'business_analysis': True,
                'multilingual': True,
                'vision': 'gemini-pro-vision' in self.model_name
            }
        }

    def reload_config(self):
        """Reload configuration (useful for API key updates)"""
        self._initialized = False
        self._setup_config()

    def update_config(self, api_key=None, model_name=None, max_tokens=None, temperature=None):
        """Update configuration and reinitialize"""
        if api_key:
            self.api_key = api_key
        if model_name:
            self.model_name = model_name
        if max_tokens:
            self.max_tokens = max_tokens
        if temperature:
            self.temperature = temperature

        # Reinitialize with new config
        if self.api_key:
            self._initialize_gemini()
    
# Global instance - singleton pattern
_gemini_config = None

def get_gemini_config() -> CentralGeminiConfig:
    """Get the global Gemini configuration instance"""
    global _gemini_config
    if _gemini_config is None:
        _gemini_config = CentralGeminiConfig()
    return _gemini_config

def is_gemini_available() -> bool:
    """Check if Gemini service is available"""
    return get_gemini_config().is_available

def generate_gemini_content(prompt: str, **kwargs) -> Optional[str]:
    """Generate content using Gemini AI with central config"""
    return get_gemini_config().generate_content(prompt, **kwargs)

def get_gemini_status() -> Dict[str, Any]:
    """Get Gemini service status"""
    return get_gemini_config().get_status()

def reload_gemini_config():
    """Reload Gemini configuration"""
    get_gemini_config().reload_config()

def update_gemini_config(api_key=None, model_name=None, max_tokens=None, temperature=None):
    """Update Gemini configuration"""
    get_gemini_config().update_config(api_key, model_name, max_tokens, temperature)

# Backward compatibility aliases
get_ai_config = get_gemini_config
is_ai_available = is_gemini_available
generate_ai_content = generate_gemini_content
get_ai_status = get_gemini_status
reload_ai_config = reload_gemini_config


# Language-specific prompt helpers for Gemini
class GeminiPrompts:
    """Centralized Gemini prompts for different languages and contexts"""

    @staticmethod
    def get_system_prompt(language: str = 'en', context: str = 'general') -> str:
        """Get system prompt based on language and context"""

        if language == 'ar':
            base_prompt = """أنت ياسمين، مساعد ذكي متخصص في ريادة الأعمال والذكاء الاصطناعي.

خصائصك:
- تتحدث العربية بطلاقة وتفهم الثقافة العربية
- خبير في تخطيط الأعمال والتحليل المالي
- متخصص في الذكاء الاصطناعي وعلوم البيانات
- تقدم نصائح عملية ومفيدة

تعليمات:
- أجب دائماً باللغة العربية الفصحى
- كن مفيداً ومهنياً ومتفهماً للسياق الثقافي
- قدم معلومات دقيقة ومحدثة"""
        else:
            base_prompt = """You are Yasmeen, an intelligent AI assistant specialized in business and entrepreneurship.

Your characteristics:
- Expert in business planning and financial analysis
- Specialized in AI and data science
- Provide practical and useful advice
- Understand cultural context and business environments

Instructions:
- Always be helpful, professional, and contextually aware
- Provide accurate and up-to-date information
- Focus on actionable business insights"""

        # Add context-specific prompts
        if context == 'business_plan':
            if language == 'ar':
                base_prompt += "\n\nأنت متخصص في إنشاء خطط الأعمال الشاملة والمفصلة."
            else:
                base_prompt += "\n\nYou specialize in creating comprehensive and detailed business plans."

        elif context == 'mentorship':
            if language == 'ar':
                base_prompt += "\n\nأنت مرشد أعمال خبير يقدم التوجيه والنصائح للرياديين."
            else:
                base_prompt += "\n\nYou are an expert business mentor providing guidance to entrepreneurs."

        return base_prompt

    @staticmethod
    def prepare_chat_prompt(message: str, language: str = 'en', context: Optional[Dict[str, Any]] = None) -> str:
        """Prepare chat prompt with system context"""
        system_prompt = GeminiPrompts.get_system_prompt(language, 'general')

        if context:
            context_info = f"\nContext: {json.dumps(context, ensure_ascii=False)}\n"
        else:
            context_info = ""

        return f"{system_prompt}{context_info}\n\nUser: {message}\n\nAssistant:"

    @staticmethod
    def prepare_business_analysis_prompt(business_idea: str, language: str = 'en') -> str:
        """Prepare business analysis prompt"""
        system_prompt = GeminiPrompts.get_system_prompt(language, 'business_plan')

        if language == 'ar':
            analysis_prompt = f"""
{system_prompt}

يرجى تحليل فكرة العمل التالية وتقديم تحليل شامل يتضمن:

1. نقاط القوة والضعف
2. الفرص والتهديدات
3. تحليل السوق المستهدف
4. التوصيات والخطوات التالية

فكرة العمل: {business_idea}

التحليل:"""
        else:
            analysis_prompt = f"""
{system_prompt}

Please analyze the following business idea and provide a comprehensive analysis including:

1. Strengths and weaknesses
2. Opportunities and threats
3. Target market analysis
4. Recommendations and next steps

Business Idea: {business_idea}

Analysis:"""

        return analysis_prompt

    @staticmethod
    def prepare_text_analysis_prompt(text: str, language: str = 'en') -> str:
        """Prepare text analysis prompt"""
        system_prompt = GeminiPrompts.get_system_prompt(language, 'general')

        if language == 'ar':
            analysis_prompt = f"""
{system_prompt}

يرجى تحليل النص التالي وتقديم:

1. الملخص الرئيسي
2. النقاط المهمة
3. التوصيات أو الاستنتاجات

النص: {text}

التحليل:"""
        else:
            analysis_prompt = f"""
{system_prompt}

Please analyze the following text and provide:

1. Main summary
2. Key points
3. Recommendations or conclusions

Text: {text}

Analysis:"""

        return analysis_prompt

# Backward compatibility
AIPrompts = GeminiPrompts



# Fallback responses when AI is not available
class AIFallbacks:
    """Centralized fallback responses"""
    
    @staticmethod
    def get_chat_fallback(language: str = 'en') -> str:
        """Get chat fallback response"""
        if language == 'ar':
            return """مرحباً! أنا ياسمين، مساعدك الذكي في ريادة الأعمال.

خدمة الذكاء الاصطناعي غير متاحة حالياً، لكن يمكنني مساعدتك في:
• تطوير أفكار الأعمال
• التخطيط الاستراتيجي
• تحليل السوق
• النصائح العملية

كيف يمكنني مساعدتك اليوم؟"""
        else:
            return """Hello! I'm Yasmeen, your intelligent business assistant.

The AI service is currently unavailable, but I can help you with:
• Business idea development
• Strategic planning
• Market analysis
• Practical advice

How can I help you today?"""
    
    @staticmethod
    def get_business_analysis_fallback(business_idea: str, language: str = 'en') -> str:
        """Get business analysis fallback"""
        if language == 'ar':
            return f"""تحليل فكرة العمل: {business_idea}

📊 تحليل أولي:

✅ نقاط القوة المحتملة:
• فكرة مبتكرة تستحق التطوير
• إمكانية لخدمة احتياجات السوق
• فرصة للنمو والتوسع

⚠️ التحديات المتوقعة:
• الحاجة لدراسة السوق المتعمقة
• تحديد الجمهور المستهدف
• وضع استراتيجية تسويق فعالة

💡 التوصيات:
• ابدأ بدراسة جدوى مفصلة
• تحدث مع العملاء المحتملين
• طور نموذج أولي للاختبار

🎯 الخطوات التالية:
1. تحديد السوق المستهدف
2. تحليل المنافسين
3. تطوير المنتج/الخدمة
4. وضع خطة التسويق"""
        else:
            return f"""Business Idea Analysis: {business_idea}

📊 Initial Analysis:

✅ Potential Strengths:
• Innovative idea worth developing
• Potential to serve market needs
• Opportunity for growth and expansion

⚠️ Expected Challenges:
• Need for in-depth market research
• Target audience identification
• Effective marketing strategy development

💡 Recommendations:
• Start with detailed feasibility study
• Talk to potential customers
• Develop prototype for testing

🎯 Next Steps:
1. Define target market
2. Analyze competitors
3. Develop product/service
4. Create marketing plan"""
    
    @staticmethod
    def get_error_message(language: str = 'en') -> str:
        """Get error message"""
        if language == 'ar':
            return "أعتذر، حدث خطأ تقني. يرجى المحاولة مرة أخرى لاحقاً."
        else:
            return "I apologize, but a technical error occurred. Please try again later."
