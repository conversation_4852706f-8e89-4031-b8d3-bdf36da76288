#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

# Now test the exact web request scenario
try:
    print("=== Simple Request Debug Test ===")
    
    from django.contrib.auth.models import User
    from core.ai_service import ai_chat_auto
    
    # Get the actual user
    user = User.objects.first()
    print(f"Using user: {user.username} (ID: {user.id})")
    
    # Simulate the exact request data processing
    request_data = {
        'message': 'Test message - are you working now?',
        'language': 'auto',
        'context': None
    }
    
    print(f"Request data: {request_data}")
    
    # Simulate the exact code from UniversalChatView.post()
    print("\nStep 1: Simulating UniversalChatView.post() logic...")
    
    try:
        # Extract data like the view does
        message = request_data.get('message', '').strip()
        language = request_data.get('language', 'auto')
        
        print(f"Extracted message: '{message}'")
        print(f"Extracted language: '{language}'")
        
        if not message:
            print("ERROR: Message is empty")
        else:
            print("Message is valid")
            
            # Use auto-detection if language is 'auto'
            if language == 'auto':
                print("Using ai_chat_auto...")
                result = ai_chat_auto(message, user.id)
                print(f"SUCCESS: {result}")
            else:
                print(f"Using ai_chat with language: {language}")
                from core.ai_service import ai_chat
                result = ai_chat(message, language, user.id)
                print(f"SUCCESS: {result}")
                
    except Exception as e:
        print(f"ERROR in processing: {e}")
        import traceback
        traceback.print_exc()

except Exception as e:
    print(f"MAIN ERROR: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 50)
print("Simple request debug test completed")