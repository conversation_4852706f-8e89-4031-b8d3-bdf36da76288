#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

# Now test the chat functionality step by step
try:
    print("=== Chat Debug Test ===")
    print("Step 1: Testing language detection...")
    
    from core.ai_service import detect_language, ai_chat_auto, ai_chat
    
    test_message = "Can you help me with my business idea analysis?"
    detected_lang = detect_language(test_message)
    print(f"Message: {test_message}")
    print(f"Detected language: {detected_lang}")
    
    print("\nStep 2: Testing AI service availability...")
    from core.ai_config import get_gemini_config
    config = get_gemini_config()
    print(f"AI Available: {config.is_available}")
    print(f"API Key: {bool(config.api_key)}")
    print(f"Model: {config.model_name}")
    
    if config.is_available:
        print("\nStep 3: Testing direct content generation...")
        test_response = config.generate_content("Hello! Please respond with 'Direct test working'")
        print(f"Direct response: {test_response}")
        
        print("\nStep 4: Testing ai_chat function...")
        chat_result = ai_chat(test_message, detected_lang, 1)
        print(f"Chat result: {chat_result}")
        
        print("\nStep 5: Testing ai_chat_auto function...")
        auto_result = ai_chat_auto(test_message, 1)
        print(f"Auto result: {auto_result}")
    else:
        print("❌ AI service not available - cannot test further")
        
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 50)
print("Debug test completed")