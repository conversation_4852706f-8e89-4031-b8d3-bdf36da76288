/**
 * Consolidated AI Chat Component
 * Advanced chat interface using the new consolidated AI service
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Send, Bot, User, Sparkles, Globe, Brain, Zap, RefreshCw, Upload, X, File, Image, FileText, Paperclip, Copy, RotateCcw, Download, Share2 } from 'lucide-react';
import { useCentralizedAI } from '../../hooks/useCentralizedAI';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { RTLText } from '../common';
import { authAPI } from '../../services/api';
import { chatAPI, ChatMessage as StoredChatMessage } from '../../services/chatApi';

interface ConsolidatedAIChatProps {
  businessIdeaId?: number;
  businessContext?: any;
  userId?: number;
  userName?: string;
  userRegion?: string;
  language?: string;
  className?: string;
}

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  language?: string;
  workflowType?: string;
  enhanced?: boolean;
  hasCulturalContext?: boolean;
  hasMLInsights?: boolean;
  isAuthError?: boolean;
  hasError?: boolean;
  originalMessage?: string;
  attachments?: string[];
  errorDetails?: string;
}

export const ConsolidatedAIChat: React.FC<ConsolidatedAIChatProps> = ({
  businessIdeaId,
  businessContext,
  userId,
  userName,
  userRegion,
  language = 'auto',
  className = '',
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [currentLanguage, setCurrentLanguage] = useState(language);
  const [isRefreshingToken, setIsRefreshingToken] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [isDragActive, setIsDragActive] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [typingDots, setTypingDots] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Use centralized AI hook
  const {
    chat,
    chatAuto,
    chatArabic,
    isChatting,
    chatError,
    isAvailable,
    status,
    refreshStatus,
  } = useCentralizedAI();

  // Enhanced availability checking with proper status indicators - Fix nested status structure
  const availability = {
    consolidatedAI: (status as any)?.status?.available || status?.available || isAvailable,
    workflows: (status as any)?.status?.features?.chat || status?.features?.chat || true,
    mlService: (status as any)?.status?.features?.business_analysis || status?.features?.business_analysis || true,
    arabicProcessing: (status as any)?.status?.features?.multilingual || status?.features?.multilingual || true,
  };

  // Connection status for UI
  const connectionStatus = {
    isConnected: availability.consolidatedAI,
    statusText: availability.consolidatedAI ? 'Connected' : 'Disconnected',
    statusColor: availability.consolidatedAI ? 'text-green-400' : 'text-red-400',
    indicatorColor: availability.consolidatedAI ? 'bg-green-400 animate-pulse' : 'bg-red-400',
  };

  // Format message content with proper HTML styling
  const formatMessageContent = (content: string): string => {
    // Escape HTML first
    const escapeHtml = (text: string) => {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    };

    let formattedContent = escapeHtml(content);

    // Format bullet points with better styling
    formattedContent = formattedContent.replace(/\* \*\*(.*?)\*\*/g, '<li class="mb-3 flex items-start"><span class="inline-block w-2 h-2 bg-purple-400 rounded-full mt-2 mr-3 flex-shrink-0"></span><div><strong class="text-purple-200 font-semibold text-sm">$1</strong></div></li>');
    formattedContent = formattedContent.replace(/\* (.*?)(?=\n|$)/g, '<li class="mb-2 flex items-start"><span class="inline-block w-1.5 h-1.5 bg-glass-muted rounded-full mt-2 mr-3 flex-shrink-0"></span><span class="text-glass-primary text-sm leading-relaxed">$1</span></li>');

    // Wrap consecutive list items in ul tags
    formattedContent = formattedContent.replace(/(<li.*?<\/li>\s*)+/g, '<ul class="space-y-1 my-4 pl-2">$&</ul>');

    // Format bold text that's not in lists
    formattedContent = formattedContent.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-purple-200">$1</strong>');

    // Format paragraphs with better spacing
    const paragraphs = formattedContent.split(/\n\s*\n/);
    formattedContent = paragraphs
      .filter(p => p.trim())
      .map(p => `<p class="mb-4 text-glass-primary leading-relaxed">${p.trim()}</p>`)
      .join('');

    // Clean up any remaining newlines in paragraphs
    formattedContent = formattedContent.replace(/\n/g, '<br class="mb-1">');

    return formattedContent;
  };

  // Handle token refresh
  const handleTokenRefresh = async () => {
    setIsRefreshingToken(true);
    try {
      const success = await authAPI.refreshToken();
      if (success) {
        // Show success message
        const successMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: t('ai.chat.sessionRefreshed', 'Session refreshed successfully! You can now continue chatting.'),
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, successMessage]);
      } else {
        // Show failure message
        const failureMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: t('ai.chat.sessionRefreshFailed', 'Session refresh failed. Please log in again to continue.'),
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, failureMessage]);
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: t('ai.chat.sessionRefreshError', 'Unable to refresh session. Please try logging in again.'),
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsRefreshingToken(false);
    }
  };

  // Improved auto-scroll to bottom
  const scrollToBottom = () => {
    setTimeout(() => {
      // Try scrolling the container first
      if (messagesContainerRef.current) {
        messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
      }

      // Also use scrollIntoView as backup
      messagesEndRef.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'end',
        inline: 'nearest'
      });
    }, 100); // Small delay to ensure DOM is updated
  };

  // Retry function for failed messages
  const retryMessage = async (originalMessage: string) => {
    if (isRetrying || retryCount >= 3) return;

    setIsRetrying(true);
    setRetryCount(prev => prev + 1);

    try {
      // Set the input message and trigger send
      setInputMessage(originalMessage);
      await handleSendMessage();
      setRetryCount(0); // Reset on success
    } catch (error) {
      console.error('Retry failed:', error);
    } finally {
      setIsRetrying(false);
    }
  };

  // File upload handlers
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setIsDragActive(true);
    } else if (e.type === 'dragleave') {
      setIsDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);

    const files = Array.from(e.dataTransfer.files);
    const validFiles = files.filter(file => {
      // Accept images, documents, and text files
      return file.type.startsWith('image/') ||
             file.type.includes('pdf') ||
             file.type.includes('document') ||
             file.type.includes('text') ||
             file.type.includes('json') ||
             file.size <= 10 * 1024 * 1024; // 10MB limit
    });

    setUploadedFiles(prev => [...prev, ...validFiles].slice(0, 5)); // Max 5 files
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const validFiles = files.filter(file => file.size <= 10 * 1024 * 1024); // 10MB limit
    setUploadedFiles(prev => [...prev, ...validFiles].slice(0, 5)); // Max 5 files
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) return <Image className="w-4 h-4" />;
    if (file.type.includes('pdf')) return <FileText className="w-4 h-4" />;
    return <File className="w-4 h-4" />;
  };

  // Load chat history on component mount
  useEffect(() => {
    loadChatHistory();
  }, [businessIdeaId]);

  // Auto-scroll when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Typing indicator animation
  useEffect(() => {
    if (isTyping) {
      typingIntervalRef.current = setInterval(() => {
        setTypingDots(prev => {
          if (prev === '...') return '.';
          return prev + '.';
        });
      }, 500);
    } else {
      if (typingIntervalRef.current) {
        clearInterval(typingIntervalRef.current);
        typingIntervalRef.current = null;
      }
      setTypingDots('');
    }

    return () => {
      if (typingIntervalRef.current) {
        clearInterval(typingIntervalRef.current);
      }
    };
  }, [isTyping]);

  // Start typing indicator
  const startTyping = () => {
    setIsTyping(true);
    setTypingDots('.');
  };

  // Stop typing indicator
  const stopTyping = () => {
    setIsTyping(false);
  };

  // Message management functions
  const copyMessage = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      // Could add a toast notification here
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const regenerateMessage = async (messageId: string) => {
    // Find the user message that prompted this AI response
    const messageIndex = messages.findIndex(m => m.id === messageId);
    if (messageIndex > 0) {
      const userMessage = messages[messageIndex - 1];
      if (userMessage.role === 'user') {
        // Remove the AI response and regenerate
        setMessages(prev => prev.slice(0, messageIndex));
        await retryMessage(userMessage.content);
      }
    }
  };

  const exportChat = () => {
    const chatContent = messages.map(msg =>
      `${msg.role === 'user' ? 'You' : 'Yasmeen'} (${msg.timestamp.toLocaleString()}): ${msg.content}`
    ).join('\n\n');

    const blob = new Blob([chatContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `yasmeen-chat-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const shareMessage = async (content: string) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Yasmeen AI Response',
          text: content,
        });
      } catch (error) {
        console.error('Failed to share:', error);
        // Fallback to copy
        await copyMessage(content);
      }
    } else {
      // Fallback to copy
      await copyMessage(content);
    }
  };

  // Load chat history from storage
  const loadChatHistory = async () => {
    try {
      const history = await chatAPI.getChatHistory(businessIdeaId);
      if (history.length > 0) {
        // Convert stored messages to our ChatMessage format
        const convertedMessages: ChatMessage[] = history.map((msg, index) => ({
          id: `stored_${index}`,
          role: msg.role,
          content: msg.content,
          timestamp: new Date(msg.timestamp),
          enhanced: msg.role === 'assistant',
        }));
        setMessages(convertedMessages);
        return;
      }
    } catch (error) {
      console.error('Error loading chat history:', error);
    }

    // If no history or error, show welcome message
    if (messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: 'welcome',
        role: 'assistant',
        content: t('ai.chat.welcomeMessage', 'Hello {{userName}}! I\'m Yasmeen, your AI assistant. How can I help you with your business ideas today?', { userName: userName || t('common.there', 'there') }),
        timestamp: new Date(),
        language: currentLanguage,
        enhanced: true,
      };
      setMessages([welcomeMessage]);
    }
  };

  // Save message to storage
  const saveMessageToHistory = async (message: ChatMessage) => {
    try {
      const storedMessage: StoredChatMessage = {
        role: message.role,
        content: message.content,
        timestamp: message.timestamp.toISOString(),
      };
      await chatAPI.storeMessage(storedMessage, businessIdeaId);
    } catch (error) {
      console.error('Error saving message to history:', error);
    }
  };

  // Clear chat history
  const clearChatHistory = async () => {
    try {
      await chatAPI.clearChatHistory(businessIdeaId);
      setMessages([]);
      // Add welcome message after clearing
      const welcomeMessage: ChatMessage = {
        id: 'welcome',
        role: 'assistant',
        content: t('ai.chat.welcomeMessage', 'Hello {{userName}}! I\'m Yasmeen, your AI assistant. How can I help you with your business ideas today?', { userName: userName || t('common.there', 'there') }),
        timestamp: new Date(),
        language: currentLanguage,
        enhanced: true,
      };
      setMessages([welcomeMessage]);
      await saveMessageToHistory(welcomeMessage);
    } catch (error) {
      console.error('Error clearing chat history:', error);
    }
  };

  // Also scroll when a message is being typed (loading state)
  useEffect(() => {
    if (isChatting) {
      scrollToBottom();
    }
  }, [isChatting]);



  const handleSendMessage = async () => {
    if ((!inputMessage.trim() && uploadedFiles.length === 0) || isChatting) return;

    // Create user message with file attachments
    let messageContent = inputMessage;
    if (uploadedFiles.length > 0) {
      const fileList = uploadedFiles.map(file => `📎 ${file.name}`).join('\n');
      messageContent = inputMessage ? `${inputMessage}\n\n${fileList}` : fileList;
    }

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: messageContent,
      timestamp: new Date(),
      attachments: uploadedFiles.length > 0 ? uploadedFiles.map(f => f.name) : undefined,
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');

    // Save user message to history
    await saveMessageToHistory(userMessage);

    // Handle file uploads if present
    if (uploadedFiles.length > 0) {
      setIsUploading(true);
    }

    // Scroll to show user message immediately
    setTimeout(() => scrollToBottom(), 50);

    // Start typing indicator
    startTyping();

    try {
      // Auto-detect language if set to auto
      const messageLanguage = currentLanguage === 'auto'
        ? 'en' // Default to English for now
        : currentLanguage;

      // Prepare chat history
      const chatHistory = messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp.toISOString(),
      }));

      // Send message using appropriate service
      const response = currentLanguage === 'ar'
        ? await chatArabic(inputMessage)
        : messageLanguage === 'auto'
        ? await chatAuto(inputMessage)
        : await chat(inputMessage, messageLanguage);

      if (response) {

        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: response.content || response.message || 'No response received',
          timestamp: new Date(),
          language: response.language || currentLanguage,
          workflowType: 'chat',
          enhanced: true,
          hasCulturalContext: currentLanguage === 'ar',
          hasMLInsights: true,
        };

        setMessages(prev => [...prev, assistantMessage]);

        // Save AI response to history
        await saveMessageToHistory(assistantMessage);

        // Stop typing indicator
        stopTyping();

        // Scroll to show AI response
        setTimeout(() => scrollToBottom(), 100);
      }
    } catch (err: any) {
      console.error('Error sending message:', err);

      // Handle authentication errors specifically
      if (err?.message?.includes('token not valid') || err?.status === 401) {
        const authErrorMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: t('ai.chat.authErrorMessage', 'Your session has expired. Please refresh the page or log in again to continue chatting.'),
          timestamp: new Date(),
          isAuthError: true, // Flag to show refresh button
        };
        setMessages(prev => [...prev, authErrorMessage]);

        // Scroll to show error message
        setTimeout(() => scrollToBottom(), 100);
      } else {
        const errorMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: t('ai.chat.errorMessage', 'Sorry, there was an error processing your message. Please try again.'),
          timestamp: new Date(),
          hasError: true,
          originalMessage: inputMessage,
          errorDetails: err.message || 'Unknown error occurred',
        };
        setMessages(prev => [...prev, errorMessage]);

        // Scroll to show error message
        setTimeout(() => scrollToBottom(), 100);

        // Auto-refresh status if connection error
        if (err.message?.includes('network') || err.message?.includes('connection') || !connectionStatus.isConnected) {
          setTimeout(() => refreshStatus(), 1000);
        }
      }

      // Always stop typing indicator on error
      stopTyping();
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getMessageIcon = (message: ChatMessage) => {
    if (message.role === 'user') {
      return <User className="w-5 h-5" />;
    }

    if (message.enhanced) {
      return <Sparkles className="w-5 h-5 text-purple-500" />;
    }

    return <Bot className="w-5 h-5" />;
  };

  const getMessageBadges = (message: ChatMessage) => {
    const badges = [];

    if (message.enhanced) {
      badges.push(
        <span key="enhanced" className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-400/30 text-purple-200 backdrop-blur-sm shadow-lg">
          <Sparkles className="w-3.5 h-3.5" />
          {t('ai.chat.enhanced', 'Enhanced')}
        </span>
      );
    }

    if (message.hasCulturalContext) {
      badges.push(
        <span key="cultural" className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-400/30 text-green-200 backdrop-blur-sm shadow-lg">
          <Globe className="w-3.5 h-3.5" />
          {t('ai.chat.cultural', 'Cultural')}
        </span>
      );
    }

    if (message.hasMLInsights) {
      badges.push(
        <span key="ml" className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border border-blue-400/30 text-blue-200 backdrop-blur-sm shadow-lg">
          <Brain className="w-3.5 h-3.5" />
          {t('ai.chat.mlInsights', 'ML Insights')}
        </span>
      );
    }

    if (message.workflowType && message.workflowType !== 'chat') {
      badges.push(
        <span key="workflow" className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-orange-500/20 to-yellow-500/20 border border-orange-400/30 text-orange-200 backdrop-blur-sm shadow-lg">
          <Zap className="w-3.5 h-3.5" />
          {message.workflowType}
        </span>
      );
    }

    return badges;
  };

  return (
    <div className={`flex flex-col h-full glass-morphism border border-glass-border rounded-2xl shadow-2xl ${className}`}>
      {/* Chat Header */}
      <div className="flex items-center justify-between p-4 md:p-6 border-b border-glass-border glass-light">
        <div className={`flex items-center gap-3 md:gap-4 min-w-0 flex-1`}>
          <div className="app-logo w-10 h-10 md:w-12 md:h-12 rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0">
            <Sparkles className="w-5 h-5 md:w-6 md:h-6 text-white" />
          </div>
          <div className="min-w-0 flex-1">
            <RTLText as="h3" className="font-bold text-glass-primary text-base md:text-lg truncate">
              {t('ai.chat.yasmeenTitle', 'Yasmeen AI Assistant')}
            </RTLText>
            <div className={`flex items-center gap-2 text-xs md:text-sm`}>
              <div className={`w-2 h-2 rounded-full ${connectionStatus.indicatorColor}`} />
              <RTLText as="span" className={`${connectionStatus.statusColor} truncate`}>
                {t(`ai.chat.${connectionStatus.isConnected ? 'connected' : 'disconnected'}`, connectionStatus.statusText)}
              </RTLText>
              {status?.service && (
                <RTLText as="span" className="text-glass-text-secondary text-xs hidden sm:inline">
                  • {status.service}
                </RTLText>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-1 md:gap-2 flex-shrink-0">
          {/* Export Chat Button */}
          {messages.length > 1 && (
            <button
              onClick={exportChat}
              className="p-2 md:p-2 glass-light hover:bg-glass-hover border border-glass-border rounded-lg text-glass-primary transition-all duration-300 hover:scale-105 touch-manipulation"
              title={t('ai.chat.exportChat', 'Export Chat')}
            >
              <Download className="w-4 h-4" />
            </button>
          )}

          {/* Clear Chat Button */}
          {messages.length > 1 && (
            <button
              onClick={clearChatHistory}
              className="p-2 md:p-2 glass-light hover:bg-glass-hover border border-glass-border rounded-lg text-glass-primary transition-all duration-300 hover:scale-105 touch-manipulation"
              title={t('ai.chat.clearHistory', 'Clear Chat History')}
            >
              <X className="w-4 h-4" />
            </button>
          )}

          {/* Refresh Status Button */}
          {!connectionStatus.isConnected && (
            <button
              onClick={() => refreshStatus()}
              className="p-2 md:p-2 glass-light hover:bg-glass-hover border border-glass-border rounded-lg text-glass-primary transition-all duration-300 hover:scale-105 touch-manipulation"
              title={t('ai.chat.refreshStatus', 'Refresh Status')}
            >
              <RefreshCw className="w-4 h-4" />
            </button>
          )}

          {/* Language Toggle */}
          <button
            onClick={() => setCurrentLanguage(currentLanguage === 'ar' ? 'en' : 'ar')}
            className="px-3 py-2 md:px-4 md:py-2 glass-light hover:bg-glass-hover border border-glass-border rounded-xl text-glass-primary text-xs md:text-sm font-medium transition-all duration-300 hover:scale-105 touch-manipulation"
          >
            {currentLanguage === 'ar' ? 'EN' : 'عربي'}
          </button>
        </div>
      </div>

      {/* Messages */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-3 md:p-6 space-y-4 md:space-y-6"
        dir={isRTL ? 'rtl' : 'ltr'}
      >
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`group flex max-w-full md:max-w-4xl gap-2 md:gap-4 ${message.role === 'user' ? (isRTL ? 'flex-row' : 'flex-row-reverse') : (isRTL ? 'flex-row-reverse' : 'flex-row')}`}>
              {/* Avatar */}
              <div className="flex-shrink-0">
                <div className={`flex items-center justify-center w-8 h-8 md:w-10 md:h-10 rounded-xl shadow-lg ${
                  message.role === 'user'
                    ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                    : 'app-logo'
                }`}>
                  {getMessageIcon(message)}
                </div>
              </div>

              {/* Message Content */}
              <div className="flex-1 min-w-0">
                <div className={`p-3 md:p-6 rounded-2xl shadow-lg backdrop-blur-sm border transition-all duration-300 hover:shadow-xl ${
                  message.role === 'user'
                    ? 'bg-gradient-to-r from-blue-500/25 to-purple-500/25 border-blue-400/40 text-white shadow-blue-500/20'
                    : 'glass-light border-glass-border text-glass-primary shadow-purple-500/10'
                }`}>
                  <div className={`prose prose-sm max-w-none ${isRTL ? 'text-right' : 'text-left'}`}>
                    <div
                      className="leading-relaxed"
                      dangerouslySetInnerHTML={{
                        __html: formatMessageContent(message.content)
                      }}
                    />
                  </div>

                  {/* Action buttons for error messages */}
                  {message.isAuthError && (
                    <div className="mt-4">
                      <button
                        onClick={handleTokenRefresh}
                        disabled={isRefreshingToken}
                        className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800/50 text-white rounded-lg font-medium transition-colors"
                      >
                        <RefreshCw className={`w-4 h-4 ${isRefreshingToken ? 'animate-spin' : ''}`} />
                        {isRefreshingToken
                          ? t('ai.chat.refreshing', 'Refreshing...')
                          : t('ai.chat.refreshSession', 'Refresh Session')
                        }
                      </button>
                    </div>
                  )}

                  {/* Retry button for general errors */}
                  {message.hasError && message.originalMessage && !message.isAuthError && (
                    <div className="mt-4 flex gap-2">
                      <button
                        onClick={() => retryMessage(message.originalMessage!)}
                        disabled={isRetrying || retryCount >= 3}
                        className="flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-800/50 text-white rounded-lg font-medium transition-colors"
                      >
                        <RefreshCw className={`w-4 h-4 ${isRetrying ? 'animate-spin' : ''}`} />
                        {isRetrying
                          ? t('ai.chat.retrying', 'Retrying...')
                          : t('ai.chat.retry', 'Retry')
                        }
                      </button>
                      {retryCount > 0 && (
                        <span className="text-xs text-red-400 self-center">
                          {t('ai.chat.retryAttempt', 'Attempt {{count}}/3', { count: retryCount })}
                        </span>
                      )}
                    </div>
                  )}
                  {/* Message action buttons */}
                  {!message.hasError && !message.isAuthError && (
                    <div className="mt-3 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      {/* Copy button */}
                      <button
                        onClick={() => copyMessage(message.content)}
                        className="p-1.5 rounded-lg hover:bg-glass-hover text-glass-text-secondary hover:text-glass-primary transition-colors"
                        title={t('ai.chat.copy', 'Copy message')}
                      >
                        <Copy className="w-3.5 h-3.5" />
                      </button>

                      {/* Regenerate button (only for AI messages) */}
                      {message.role === 'assistant' && (
                        <button
                          onClick={() => regenerateMessage(message.id)}
                          className="p-1.5 rounded-lg hover:bg-glass-hover text-glass-text-secondary hover:text-glass-primary transition-colors"
                          title={t('ai.chat.regenerate', 'Regenerate response')}
                        >
                          <RotateCcw className="w-3.5 h-3.5" />
                        </button>
                      )}

                      {/* Share button */}
                      <button
                        onClick={() => shareMessage(message.content)}
                        className="p-1.5 rounded-lg hover:bg-glass-hover text-glass-text-secondary hover:text-glass-primary transition-colors"
                        title={t('ai.chat.share', 'Share message')}
                      >
                        <Share2 className="w-3.5 h-3.5" />
                      </button>
                    </div>
                  )}
                </div>

                {/* Message badges */}
                {message.role === 'assistant' && !message.isAuthError && (
                  <div className={`flex flex-wrap gap-2 mt-5 ${isRTL ? 'justify-end' : 'justify-start'}`}>
                    {getMessageBadges(message)}
                  </div>
                )}

                <RTLText as="p" className="text-xs text-glass-muted/70 mt-4 font-medium">
                  {message.timestamp.toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                  })}
                </RTLText>
              </div>
            </div>
          </div>
        ))}

        {(isChatting || isTyping) && (
          <div className={`flex ${isRTL ? 'justify-end' : 'justify-start'}`}>
            <div className={`flex items-center gap-4`}>
              <div className="app-logo w-10 h-10 rounded-xl flex items-center justify-center shadow-lg">
                <Bot className="w-5 h-5 text-white animate-pulse" />
              </div>
              <div className="glass-light border border-glass-border rounded-2xl p-4 shadow-lg backdrop-blur-sm">
                <div className={`flex items-center gap-3`}>
                  <div className={`flex gap-1`}>
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                  </div>
                  <RTLText as="span" className="text-glass-text-secondary text-sm">
                    {t('ai.chat.typing', 'Yasmeen is typing')}
                    <span className="text-purple-400 font-mono">{typingDots}</span>
                  </RTLText>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Section */}
      <div className="p-6 border-t border-glass-border glass-light">
        {chatError && (
          <div className="mb-4 p-4 bg-red-500/20 border border-red-500/30 rounded-xl text-red-300 text-sm backdrop-blur-sm">
            <div className={`flex items-center gap-2`}>
              <Bot size={16} className="text-red-400" />
              <RTLText as="span">{chatError}</RTLText>
            </div>
          </div>
        )}

        <div className={`flex gap-2 md:gap-4`}>
          <textarea
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder={t('ai.chat.messagePlaceholder', 'Type your message here...')}
            className="input flex-1 rounded-2xl resize-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300 text-sm md:text-base"
            rows={window.innerWidth < 768 ? 2 : 3}
            dir={isRTL ? 'rtl' : 'ltr'}
            disabled={isChatting}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isChatting}
            className="px-4 py-3 md:px-6 md:py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-2xl hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl touch-manipulation"
          >
            <Send className="w-4 h-4 md:w-5 md:h-5" />
          </button>
        </div>

        {/* Status indicators */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-2 gap-2 text-xs text-glass-muted">
          <div className="flex items-center gap-2 md:gap-4 flex-wrap">
            {availability.workflows && (
              <span className="flex items-center gap-1">
                <Zap className="w-3 h-3 text-orange-400" />
                <span className="hidden sm:inline">{t('ai.chat.advancedWorkflows', 'Advanced Workflows')}</span>
                <span className="sm:hidden">{t('ai.chat.workflows', 'Workflows')}</span>
              </span>
            )}
            {availability.mlService && (
              <span className="flex items-center gap-1">
                <Brain className="w-3 h-3 text-blue-400" />
                <span className="hidden sm:inline">{t('ai.chat.mlInsights', 'ML Insights')}</span>
                <span className="sm:hidden">{t('ai.chat.ml', 'ML')}</span>
              </span>
            )}
            {availability.arabicProcessing && (
              <span className="flex items-center gap-1">
                <Globe className="w-3 h-3 text-green-400" />
                <span className="hidden sm:inline">{t('ai.chat.cultural', 'Cultural Context')}</span>
                <span className="sm:hidden">{t('ai.chat.arabic', 'Arabic')}</span>
              </span>
            )}
          </div>

          {inputMessage.length > 100 && (
            <span className="text-orange-400 text-xs">
              <span className="hidden sm:inline">{t('ai.chat.complexQuery', 'Complex query - Advanced workflow will be used')}</span>
              <span className="sm:hidden">{t('ai.chat.complexQueryShort', 'Advanced mode')}</span>
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default ConsolidatedAIChat;
