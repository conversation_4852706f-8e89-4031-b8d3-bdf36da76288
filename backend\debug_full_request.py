#!/usr/bin/env python
import os
import sys
import django
import json

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

# Now test the exact web request scenario
try:
    print("=== Full Request Debug Test ===")
    
    from django.test import RequestFactory
    from django.contrib.auth.models import User
    from core.ai_views import UniversalChatView
    from rest_framework.test import force_authenticate
    
    # Get the actual user
    user = User.objects.first()
    print(f"Using user: {user.username} (ID: {user.id})")
    
    # Create a request factory
    factory = RequestFactory()
    
    # Create the exact request that the frontend would send
    request_data = {
        'message': 'Test message - are you working now?',
        'language': 'auto',
        'context': None
    }
    
    print(f"Request data: {request_data}")
    
    # Create a POST request
    request = factory.post(
        '/api/ai/chat/',
        data=json.dumps(request_data),
        content_type='application/json'
    )
    
    # Authenticate the request
    force_authenticate(request, user=user)
    
    print("\nStep 1: Testing UniversalChatView directly...")
    
    # Create the view and call it
    view = UniversalChatView()
    
    try:
        response = view.post(request)
        print(f"Response status: {response.status_code}")
        print(f"Response data: {response.data}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: Chat request worked!")
        else:
            print(f"❌ ERROR: Chat request failed with status {response.status_code}")
            
    except Exception as e:
        print(f"❌ EXCEPTION in view.post(): {e}")
        import traceback
        traceback.print_exc()

except Exception as e:
    print(f"MAIN ERROR: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 50)
print("Full request debug test completed")