#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

# Now test the AI configuration
try:
    print("Testing AI Configuration...")
    print("=" * 50)
    
    # Test 1: Import and check configuration
    from core.ai_config import get_gemini_config, reload_gemini_config
    
    # Force reload to test new configuration
    reload_gemini_config()
    
    config = get_gemini_config()
    
    print(f"Service: gemini")
    print(f"Available: {config.is_available}")
    print(f"Model: {config.model_name}")
    print(f"API Key Configured: {bool(config.api_key)}")
    if config.api_key:
        print(f"API Key: {config.api_key[:10]}...{config.api_key[-4:]}")
    
    # Test 2: Get status
    status = config.get_status()
    print(f"\nStatus: {status}")
    
    # Test 3: Test AI service
    if config.is_available:
        print("\nTesting AI service...")
        from core.ai_service import ai_chat
        response = ai_chat("Hello! Please respond with 'AI is working'", "en")
        
        if response.get('success'):
            print("SUCCESS: AI Chat test PASSED!")
            print(f"Response: {response.get('data', {}).get('message', 'No message')}")
        else:
            print(f"ERROR: AI Chat test failed: {response.get('error')}")
    else:
        print("ERROR: AI service is not available")
        
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 50)
print("Test completed")